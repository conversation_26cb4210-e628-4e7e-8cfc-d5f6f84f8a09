<?php

namespace app\common\library;

/**
 * DES加密类 - 与Java端加密算法保持一致
 * 基于Java端com.iwesalt.common.utils.des类的完整实现
 */
class DesEncryption
{
    // Java端使用的密钥
    private static $COMM_KEY = [
        105, 106, 109, 101, 100, 111, 107, 109, 
        102, 97, 111, 104, 102, 97, 111, 105
    ];
    
    // DES算法相关常量
    public static $masterKey = "CC155E8427931E3A129ADD1D7615B81D";
    public static $masterKey1 = "00804000000000000103050101010100";
    public static $DES_KEY = [100, 108, 107, 112, 102, 106, 97, 115, 100, 106, 97, 115, 100, 109, 102, 107];
    
    private static $IP = [58, 50, 42, 34, 26, 18, 10, 2, 60, 52, 44, 36, 28, 20, 12, 4, 62, 54, 46, 38, 30, 22, 14, 6, 64, 56, 48, 40, 32, 24, 16, 8, 57, 49, 41, 33, 25, 17, 9, 1, 59, 51, 43, 35, 27, 19, 11, 3, 61, 53, 45, 37, 29, 21, 13, 5, 63, 55, 47, 39, 31, 23, 15, 7];
    
    private static $IP_1 = [40, 8, 48, 16, 56, 24, 64, 32, 39, 7, 47, 15, 55, 23, 63, 31, 38, 6, 46, 14, 54, 22, 62, 30, 37, 5, 45, 13, 53, 21, 61, 29, 36, 4, 44, 12, 52, 20, 60, 28, 35, 3, 43, 11, 51, 19, 59, 27, 34, 2, 42, 10, 50, 18, 58, 26, 33, 1, 41, 9, 49, 17, 57, 25];
    
    private static $PC_1 = [57, 49, 41, 33, 25, 17, 9, 1, 58, 50, 42, 34, 26, 18, 10, 2, 59, 51, 43, 35, 27, 19, 11, 3, 60, 52, 44, 36, 63, 55, 47, 39, 31, 23, 15, 7, 62, 54, 46, 38, 30, 22, 14, 6, 61, 53, 45, 37, 29, 21, 13, 5, 28, 20, 12, 4];
    
    private static $PC_2 = [14, 17, 11, 24, 1, 5, 3, 28, 15, 6, 21, 10, 23, 19, 12, 4, 26, 8, 16, 7, 27, 20, 13, 2, 41, 52, 31, 37, 47, 55, 30, 40, 51, 45, 33, 48, 44, 49, 39, 56, 34, 53, 46, 42, 50, 36, 29, 32];
    
    private static $E = [32, 1, 2, 3, 4, 5, 4, 5, 6, 7, 8, 9, 8, 9, 10, 11, 12, 13, 12, 13, 14, 15, 16, 17, 16, 17, 18, 19, 20, 21, 20, 21, 22, 23, 24, 25, 24, 25, 26, 27, 28, 29, 28, 29, 30, 31, 32, 1];
    
    private static $P = [16, 7, 20, 21, 29, 12, 28, 17, 1, 15, 23, 26, 5, 18, 31, 10, 2, 8, 24, 14, 32, 27, 3, 9, 19, 13, 30, 6, 22, 11, 4, 25];
    
    private static $S_Box = [
        [[[14, 4, 13, 1, 2, 15, 11, 8, 3, 10, 6, 12, 5, 9, 0, 7], [0, 15, 7, 4, 14, 2, 13, 1, 10, 6, 12, 11, 9, 5, 3, 8], [4, 1, 14, 8, 13, 6, 2, 11, 15, 12, 9, 7, 3, 10, 5, 0], [15, 12, 8, 2, 4, 9, 1, 7, 5, 11, 3, 14, 10, 0, 6, 13]]],
        [[[15, 1, 8, 14, 6, 11, 3, 4, 9, 7, 2, 13, 12, 0, 5, 10], [3, 13, 4, 7, 15, 2, 8, 14, 12, 0, 1, 10, 6, 9, 11, 5], [0, 14, 7, 11, 10, 4, 13, 1, 5, 8, 12, 6, 9, 3, 2, 15], [13, 8, 10, 1, 3, 15, 4, 2, 11, 6, 7, 12, 0, 5, 14, 9]]],
        [[[10, 0, 9, 14, 6, 3, 15, 5, 1, 13, 12, 7, 11, 4, 2, 8], [13, 7, 0, 9, 3, 4, 6, 10, 2, 8, 5, 14, 12, 11, 15, 1], [13, 6, 4, 9, 8, 15, 3, 0, 11, 1, 2, 12, 5, 10, 14, 7], [1, 10, 13, 0, 6, 9, 8, 7, 4, 15, 14, 3, 11, 5, 2, 12]]],
        [[[7, 13, 14, 3, 0, 6, 9, 10, 1, 2, 8, 5, 11, 12, 4, 15], [13, 8, 11, 5, 6, 15, 0, 3, 4, 7, 2, 12, 1, 10, 14, 9], [10, 6, 9, 0, 12, 11, 7, 13, 15, 1, 3, 14, 5, 2, 8, 4], [3, 15, 0, 6, 10, 1, 13, 8, 9, 4, 5, 11, 12, 7, 2, 14]]],
        [[[2, 12, 4, 1, 7, 10, 11, 6, 8, 5, 3, 15, 13, 0, 14, 9], [14, 11, 2, 12, 4, 7, 13, 1, 5, 0, 15, 10, 3, 9, 8, 6], [4, 2, 1, 11, 10, 13, 7, 8, 15, 9, 12, 5, 6, 3, 0, 14], [11, 8, 12, 7, 1, 14, 2, 13, 6, 15, 0, 9, 10, 4, 5, 3]]],
        [[[12, 1, 10, 15, 9, 2, 6, 8, 0, 13, 3, 4, 14, 7, 5, 11], [10, 15, 4, 2, 7, 12, 9, 5, 6, 1, 13, 14, 0, 11, 3, 8], [9, 14, 15, 5, 2, 8, 12, 3, 7, 0, 4, 10, 1, 13, 11, 6], [4, 3, 2, 12, 9, 5, 15, 10, 11, 14, 1, 7, 6, 0, 8, 13]]],
        [[[4, 11, 2, 14, 15, 0, 8, 13, 3, 12, 9, 7, 5, 10, 6, 1], [13, 0, 11, 7, 4, 9, 1, 10, 14, 3, 5, 12, 2, 15, 8, 6], [1, 4, 11, 13, 12, 3, 7, 14, 10, 15, 6, 8, 0, 5, 9, 2], [6, 11, 13, 8, 1, 4, 10, 7, 9, 5, 0, 15, 14, 2, 3, 12]]],
        [[[13, 2, 8, 4, 6, 15, 11, 1, 10, 9, 3, 14, 5, 0, 12, 7], [1, 15, 13, 8, 10, 3, 7, 4, 12, 5, 6, 11, 0, 14, 9, 2], [7, 11, 4, 1, 9, 12, 14, 2, 0, 6, 10, 13, 15, 3, 5, 8], [2, 1, 14, 7, 4, 10, 8, 13, 15, 12, 9, 0, 3, 5, 6, 11]]]
    ];
    
    /**
     * 加密数据
     * @param string $data 要加密的数据
     * @return string 加密后的16进制字符串
     */
    public static function encrypt($data)
    {
        $dataBytes = self::stringToBytes($data);
        $encrypted = self::encryptDecryptBuf($dataBytes);
        return self::bytesToHexString($encrypted);
    }
    
    /**
     * 解密数据
     * @param string $hexData 16进制格式的加密数据
     * @return string 解密后的原始数据
     */
    public static function decrypt($hexData)
    {
        $dataBytes = self::hexStringToBytes($hexData);
        $decrypted = self::encryptDecryptBuf($dataBytes);
        return self::bytesToString($decrypted);
    }
    
    /**
     * 与Java端encryptDecryptBuf方法对应的实现
     * @param array $pBuf 字节数组
     * @return array 处理后的字节数组
     */
    public static function encryptDecryptBuf($pBuf)
    {
        $j = 0;
        $result = [];
        
        for ($i = 0; $i < count($pBuf); $i++) {
            $result[$i] = ($pBuf[$i] ^ self::$COMM_KEY[$j]) & 0xFF;
            if (++$j == count(self::$COMM_KEY)) {
                $j = 0;
            }
        }
        
        return $result;
    }
    
    /**
     * short转byte
     */
    private static function shortToByte($v)
    {
        $value = $v & 0xFF;
        if ($value <= 127) {
            return $value;
        }
        $baseB = 1;
        $baseB = $baseB << 7;
        $valueB = $value - 128;
        $valueB = $valueB | $baseB;
        return $valueB;
    }

    /**
     * 字节数组切片
     */
    private static function byteArray2byteArray($sdata, $startaddr, $len)
    {
        return array_slice($sdata, $startaddr, $len);
    }

    /**
     * 压缩48位
     */
    private static function compact48(&$p1, $p2)
    {
        for ($i = 0; $i < 48; $i++) {
            $j = self::$PC_2[$i] - 1;
            $p1[$i] = $p2[$j];
        }
    }

    /**
     * 扩展48位
     */
    private static function expand48(&$p1, $p2)
    {
        for ($i = 0; $i < 48; $i++) {
            $j = self::$E[$i] - 1;
            $p1[$i] = $p2[$j];
        }
    }

    /**
     * 左移
     */
    private static function l_shift(&$p1, $cl)
    {
        switch ($cl) {
            case 1:
            case 2:
            case 9:
            case 16:
                $i = 1;
                break;
            default:
                $i = 2;
        }
        for ($j = 0; $j < $i; $j++) {
            $buf = $p1[0];
            for ($k = 0; $k < 27; $k++) {
                $p1[$k] = $p1[$k + 1];
            }
            $p1[27] = $buf;
        }
    }

    /**
     * 右移
     */
    private static function r_shift(&$p1, $cl)
    {
        switch ($cl) {
            case 1:
            case 2:
            case 9:
            case 16:
                $i = 1;
                break;
            default:
                $i = 2;
        }
        for ($j = 0; $j < $i; $j++) {
            $buf = $p1[27];
            for ($k = 26; $k >= 0; $k--) {
                $p1[$k + 1] = $p1[$k];
            }
            $p1[0] = $buf;
        }
    }

    /**
     * 二维数组转一维数组
     */
    private static function Int2DArrayToIntBuffer($src)
    {
        $desc = [];
        for ($i = 0; $i < 8; $i++) {
            for ($j = 0; $j < 8; $j++) {
                $desc[$i * 8 + $j] = $src[$i][$j];
            }
        }
        return $desc;
    }

    /**
     * XOR运算
     */
    private static function XOR_XOR($ptrStr1, $ptrStr2)
    {
        $ret = [];
        for ($i = 0; $i < count($ptrStr1); $i++) {
            $ret[$i] = $ptrStr1[$i] ^ $ptrStr2[$i];
        }
        return $ret;
    }
    
    /**
     * 字符串转字节数组
     * @param string $str
     * @return array
     */
    private static function stringToBytes($str)
    {
        $bytes = [];
        for ($i = 0; $i < strlen($str); $i++) {
            $bytes[] = ord($str[$i]);
        }
        return $bytes;
    }
    
    /**
     * 字节数组转字符串
     * @param array $bytes
     * @return string
     */
    private static function bytesToString($bytes)
    {
        $str = '';
        foreach ($bytes as $byte) {
            $str .= chr($byte);
        }
        return $str;
    }
    
    /**
     * 16进制字符串转字节数组
     * @param string $hex
     * @return array
     */
    private static function hexStringToBytes($hex)
    {
        $hex = str_replace(' ', '', $hex);
        $bytes = [];
        
        for ($i = 0; $i < strlen($hex); $i += 2) {
            $bytes[] = hexdec(substr($hex, $i, 2));
        }
        
        return $bytes;
    }
    
    /**
     * 字节数组转16进制字符串
     * @param array $bytes
     * @return string
     */
    private static function bytesToHexString($bytes)
    {
        $hex = '';
        foreach ($bytes as $byte) {
            $hex .= sprintf('%02x', $byte);
        }
        return $hex;
    }
    
    /**
     * 字节数组转十六进制字符串（Java兼容版本）
     */
    public static function bytesToHexStringJava($barray)
    {
        $str = '';
        for ($i = 0; $i < count($barray); $i++) {
            $sr = $barray[$i] & 0xF;
            $sl = ($barray[$i] & 0xFF) >> 4 & 0xF;
            $c = ($sl >= 0 && $sl <= 9) ? chr(48 + $sl) : chr(65 + $sl - 10);
            $str .= $c;
            $c = ($sr >= 0 && $sr <= 9) ? chr(48 + $sr) : chr(65 + $sr - 10);
            $str .= $c;
        }
        return $str;
    }

    /**
     * 十六进制字符串转字节数组（Java兼容版本）
     */
    public static function hexStringToBytesJava($s)
    {
        $b = [];
        for ($i = 0; $i < strlen($s) / 2; $i++) {
            $l = $s[$i * 2];
            $r = $s[$i * 2 + 1];
            $tv = ($l >= '0' && $l <= '9') ? (ord($l) - 48) : (($l >= 'A' && $l <= 'F') ? (ord($l) - 65 + 10) : (($l >= 'a' && $l <= 'f') ? (ord($l) - 97 + 10) : 0));
            $tv = $tv << 4;
            $tv = ($r >= '0' && $r <= '9') ? ($tv | (ord($r) - 48)) : (($r >= 'A' && $r <= 'F') ? ($tv | (ord($r) - 65 + 10)) : (($r >= 'a' && $r <= 'f') ? ($tv | (ord($r) - 97 + 10)) : 0));
            $b[$i] = self::shortToByte($tv);
        }
        return $b;
    }

    /**
     * DES解密
     */
    public static function UnDes($ptrKey, $ptrData, &$ptrCipher)
    {
        $buf = [0, 0];
        $array = array_fill(0, 64, 0);
        $ln = array_fill(0, 32, 0);
        $rn = array_fill(0, 32, 0);
        $mm = array_fill(0, 32, 0);
        $lx = array_fill(0, 48, 0);
        $key_ln = array_fill(0, 28, 0);
        $key_rn = array_fill(0, 28, 0);
        $key1 = array_fill(0, 56, 0);
        $key2 = array_fill(0, 48, 0);
        $message_index = array_fill(0, 8, array_fill(0, 8, 0));
        $key_index = array_fill(0, 8, array_fill(0, 8, 0));

        for ($i = 0; $i < 8; $i++) {
            for ($j = 0; $j < 8; $j++) {
                $buf[0] = $ptrData[$i] << $j;
                $buf[1] = $ptrKey[$i] << $j;
                $message_index[$i][$j] = ($buf[0] & 0x80) == 128 ? 1 : 0;
                $key_index[$i][$j] = ($buf[1] & 0x80) == 128 ? 1 : 0;
            }
        }

        $message_index1 = self::Int2DArrayToIntBuffer($message_index);
        $key_index1 = self::Int2DArrayToIntBuffer($key_index);

        for ($i = 0; $i < 64; $i++) {
            $array[$i] = $message_index1[self::$IP[$i] - 1];
        }

        for ($i = 0; $i < 32; $i++) {
            $ln[$i] = $array[32 + $i];
            $rn[$i] = $array[$i];
        }

        for ($i = 0; $i < 56; $i++) {
            $key1[$i] = $key_index1[self::$PC_1[$i] - 1];
        }

        for ($i = 0; $i < 28; $i++) {
            $key_ln[$i] = $key1[$i];
            $key_rn[$i] = $key1[28 + $i];
        }

        for ($i = 1; $i <= 16; $i++) {
            self::l_shift($key_ln, $i);
            self::l_shift($key_rn, $i);
            for ($j = 0; $j < 28; $j++) {
                $key1[$j] = $key_ln[$j];
                $key1[28 + $j] = $key_rn[$j];
            }
        }

        for ($i = 16; $i >= 1; $i--) {
            for ($j = 0; $j < 32; $j++) {
                $mm[$j] = $ln[$j];
            }
            for ($j = 0; $j < 28; $j++) {
                $key1[$j] = $key_ln[$j];
                $key1[28 + $j] = $key_rn[$j];
            }
            self::compact48($key2, $key1);
            self::expand48($lx, $ln);
            for ($j = 0; $j < 48; $j++) {
                $lx[$j] = $lx[$j] == $key2[$j] ? 0 : 1;
            }
            for ($j = 0; $j < 8; $j++) {
                $line = $lx[6 * $j] * 2 + $lx[6 * $j + 5];
                $row = (($lx[6 * $j + 1] * 2 + $lx[6 * $j + 2]) * 2 + $lx[6 * $j + 3]) * 2 + $lx[6 * $j + 4];
                $ret = self::$S_Box[$j][0][$line][$row];
                for ($k = 3; $k >= 0; $k--) {
                    $ln[4 * $j + $k] = $ret % 2;
                    $ret = intval($ret / 2);
                }
            }
            for ($j = 0; $j < 32; $j++) {
                $array[$j] = $ln[self::$P[$j] - 1];
            }
            for ($j = 0; $j < 32; $j++) {
                $ln[$j] = $array[$j] == $rn[$j] ? 0 : 1;
            }
            for ($j = 0; $j < 32; $j++) {
                $rn[$j] = $mm[$j];
            }
            self::r_shift($key_ln, $i);
            self::r_shift($key_rn, $i);
        }

        for ($i = 0; $i < 32; $i++) {
            $array[$i] = $ln[$i];
            $array[32 + $i] = $rn[$i];
        }

        for ($i = 0; $i < 64; $i++) {
            $message_index1[$i] = $array[self::$IP_1[$i] - 1];
        }

        for ($i = 0; $i < 8; $i++) {
            $buf[0] = 0;
            for ($j = 0; $j < 8; $j++) {
                $buf[0] = $buf[0] * 2 + $message_index1[8 * $i + $j];
            }
            $ptrCipher[$i] = $buf[0];
        }
    }

    /**
     * DES加密
     */
    public static function Des($ptrKey, $ptrData, &$ptrCipher)
    {
        $buf = [0, 0];
        $array = array_fill(0, 64, 0);
        $ln = array_fill(0, 32, 0);
        $rn = array_fill(0, 32, 0);
        $mm = array_fill(0, 32, 0);
        $lx = array_fill(0, 48, 0);
        $key_ln = array_fill(0, 28, 0);
        $key_rn = array_fill(0, 28, 0);
        $key1 = array_fill(0, 56, 0);
        $key2 = array_fill(0, 48, 0);
        $message_index = array_fill(0, 8, array_fill(0, 8, 0));
        $key_index = array_fill(0, 8, array_fill(0, 8, 0));

        for ($i = 0; $i < 8; $i++) {
            for ($j = 0; $j < 8; $j++) {
                $buf[0] = $ptrData[$i] << $j;
                $buf[1] = $ptrKey[$i] << $j;
                $message_index[$i][$j] = ($buf[0] & 0x80) == 128 ? 1 : 0;
                $key_index[$i][$j] = ($buf[1] & 0x80) == 128 ? 1 : 0;
            }
        }

        $message_index1 = self::Int2DArrayToIntBuffer($message_index);
        $key_index1 = self::Int2DArrayToIntBuffer($key_index);

        for ($i = 0; $i < 64; $i++) {
            $array[$i] = $message_index1[self::$IP[$i] - 1];
        }

        for ($i = 0; $i < 32; $i++) {
            $ln[$i] = $array[$i];
            $rn[$i] = $array[32 + $i];
        }

        for ($i = 0; $i < 56; $i++) {
            $key1[$i] = $key_index1[self::$PC_1[$i] - 1];
        }

        for ($i = 0; $i < 28; $i++) {
            $key_ln[$i] = $key1[$i];
            $key_rn[$i] = $key1[28 + $i];
        }

        for ($i = 1; $i <= 16; $i++) {
            for ($j = 0; $j < 32; $j++) {
                $mm[$j] = $rn[$j];
            }
            self::l_shift($key_ln, $i);
            self::l_shift($key_rn, $i);
            for ($j = 0; $j < 28; $j++) {
                $key1[$j] = $key_ln[$j];
                $key1[28 + $j] = $key_rn[$j];
            }
            self::compact48($key2, $key1);
            self::expand48($lx, $rn);
            for ($j = 0; $j < 48; $j++) {
                $lx[$j] = $lx[$j] == $key2[$j] ? 0 : 1;
            }
            for ($j = 0; $j < 8; $j++) {
                $line = $lx[6 * $j] * 2 + $lx[6 * $j + 5];
                $row = (($lx[6 * $j + 1] * 2 + $lx[6 * $j + 2]) * 2 + $lx[6 * $j + 3]) * 2 + $lx[6 * $j + 4];
                $ret = self::$S_Box[$j][0][$line][$row];
                for ($k = 3; $k >= 0; $k--) {
                    $rn[4 * $j + $k] = $ret % 2;
                    $ret = intval($ret / 2);
                }
            }
            for ($j = 0; $j < 32; $j++) {
                $array[$j] = $rn[self::$P[$j] - 1];
            }
            for ($j = 0; $j < 32; $j++) {
                $rn[$j] = $array[$j] == $ln[$j] ? 0 : 1;
            }
            for ($j = 0; $j < 32; $j++) {
                $ln[$j] = $mm[$j];
            }
        }

        for ($i = 0; $i < 32; $i++) {
            $array[$i] = $rn[$i];
            $array[32 + $i] = $ln[$i];
        }

        for ($i = 0; $i < 64; $i++) {
            $message_index1[$i] = $array[self::$IP_1[$i] - 1];
        }

        for ($i = 0; $i < 8; $i++) {
            $buf[0] = 0;
            for ($j = 0; $j < 8; $j++) {
                $buf[0] = $buf[0] * 2 + $message_index1[8 * $i + $j];
            }
            $ptrCipher[$i] = $buf[0];
        }
    }

    /**
     * 三重DES加密
     */
    public static function TripleDes($key, $bufSource, &$bufTarget, $len)
    {
        $tempbuff = array_fill(0, 8, 0);
        $tempbuff1 = array_fill(0, 8, 0);
        for ($ctemp = 0; $ctemp < $len / 8; $ctemp++) {
            self::Des($key, self::byteArray2byteArray($bufSource, $ctemp * 8, 8), $tempbuff);
            self::UnDes(self::byteArray2byteArray($key, 8, 8), $tempbuff, $tempbuff1);
            self::Des($key, $tempbuff1, $tempbuff);
            for ($i = 0; $i < 8; $i++) {
                $bufTarget[$ctemp * 8 + $i] = $tempbuff[$i];
            }
        }
    }

    /**
     * 三重DES解密
     */
    public static function TripleUnDes($key, $bufSource, &$bufTarget, $len)
    {
        $tempbuff = array_fill(0, 8, 0);
        $tempbuff1 = array_fill(0, 8, 0);
        for ($ctemp = 0; $ctemp < $len / 8; $ctemp++) {
            self::UnDes($key, self::byteArray2byteArray($bufSource, $ctemp * 8, 8), $tempbuff);
            self::Des(self::byteArray2byteArray($key, 8, 8), $tempbuff, $tempbuff1);
            self::UnDes($key, $tempbuff1, $tempbuff);
            for ($i = 0; $i < 8; $i++) {
                $bufTarget[$ctemp * 8 + $i] = $tempbuff[$i];
            }
        }
    }

    /**
     * DES加密（字符串版本）
     */
    public static function DesString($src, $key)
    {
        $sData = self::hexStringToBytesJava($src);
        $sKey = self::hexStringToBytesJava($key);
        $sReply = array_fill(0, strlen($src) / 2, 0);
        self::Des($sKey, $sData, $sReply);
        return self::bytesToHexStringJava($sReply);
    }

    /**
     * DES解密（字符串版本）
     */
    public static function UnDesString($src, $key)
    {
        $sData = self::hexStringToBytesJava($src);
        $sKey = self::hexStringToBytesJava($key);
        $sReply = array_fill(0, strlen($src) / 2, 0);
        self::UnDes($sKey, $sData, $sReply);
        return self::bytesToHexStringJava($sReply);
    }

    /**
     * 三重DES加密（字符串版本）
     */
    public static function TripleDesString($src, $key)
    {
        $sTemp = self::hexStringToBytesJava($src);
        $sKey = self::hexStringToBytesJava($key);
        $sReply = array_fill(0, strlen($src) / 2, 0);
        self::TripleDes($sKey, $sTemp, $sReply, count($sTemp));
        return self::bytesToHexStringJava($sReply);
    }

    /**
     * 三重DES解密（字符串版本）
     */
    public static function TripleUnDesString($src, $key)
    {
        $sTemp = self::hexStringToBytesJava($src);
        $sKey = self::hexStringToBytesJava($key);
        $sReply = array_fill(0, strlen($src) / 2, 0);
        self::TripleUnDes($sKey, $sTemp, $sReply, count($sTemp));
        return self::bytesToHexStringJava($sReply);
    }
    
    /**
     * 生成二维码加密数据
     * @param array $qrData 二维码原始数据
     * @return string 加密后的数据
     */
    public static function generateQrCode($qrData)
    {
        $jsonData = json_encode($qrData, JSON_UNESCAPED_UNICODE);
        $encrypted = self::encrypt($jsonData);
        return strtoupper($encrypted);
    }
    
    /**
     * 解析二维码加密数据
     * @param string $encryptedData 加密数据
     * @return array 解密后的数据
     */
    public static function parseQrCode($encryptedData)
    {
        try {
            $decrypted = self::decrypt($encryptedData);
            $data = json_decode($decrypted, true);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new \Exception('Invalid JSON data');
            }
            
            return $data;
        } catch (\Exception $e) {
            throw new \Exception('二维码解析失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 验证二维码数据格式
     * @param array $data
     * @return bool
     */
    public static function validateQrData($data)
    {
        $requiredFields = ['id', 'filmSeeionId', 'userId', 'name', 'sfid', 'showTime', 'time', 'code', 'codeType'];
        
        foreach ($requiredFields as $field) {
            if (!isset($data[$field])) {
                return false;
            }
        }
        
        return true;
    }
}