<?php if (!defined('THINK_PATH')) exit(); /*a:4:{s:76:"/mnt/f/kejiguan/php/public/../application/admin/view/film/session/index.html";i:1753846497;s:62:"/mnt/f/kejiguan/php/application/admin/view/layout/default.html";i:1753348167;s:59:"/mnt/f/kejiguan/php/application/admin/view/common/meta.html";i:1753348167;s:61:"/mnt/f/kejiguan/php/application/admin/view/common/script.html";i:1753348167;}*/ ?>
<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
<title><?php echo (isset($title) && ($title !== '')?$title:''); ?></title>
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
<meta name="renderer" content="webkit">
<meta name="referrer" content="never">
<meta name="robots" content="noindex, nofollow">

<link rel="shortcut icon" href="/assets/img/favicon.ico" />
<!-- Loading Bootstrap -->
<link href="/assets/css/backend<?php echo \think\Config::get('app_debug')?'':'.min'; ?>.css?v=<?php echo htmlentities(\think\Config::get('site.version') ?? ''); ?>" rel="stylesheet">

<?php if(\think\Config::get('fastadmin.adminskin')): ?>
<link href="/assets/css/skins/<?php echo htmlentities(\think\Config::get('fastadmin.adminskin') ?? ''); ?>.css?v=<?php echo htmlentities(\think\Config::get('site.version') ?? ''); ?>" rel="stylesheet">
<?php endif; ?>

<!-- HTML5 shim, for IE6-8 support of HTML5 elements. All other JS at the end of file. -->
<!--[if lt IE 9]>
  <script src="/assets/js/html5shiv.js"></script>
  <script src="/assets/js/respond.min.js"></script>
<![endif]-->
<script type="text/javascript">
    var require = {
        config:  <?php echo json_encode($config ?? ''); ?>
    };
</script>

    </head>

    <body class="inside-header inside-aside <?php echo defined('IS_DIALOG') && IS_DIALOG ? 'is-dialog' : ''; ?>">
        <div id="main" role="main">
            <div class="tab-content tab-addtabs">
                <div id="content">
                    <div class="row">
                        <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
                            <section class="content-header hide">
                                <h1>
                                    <?php echo __('Dashboard'); ?>
                                    <small><?php echo __('Control panel'); ?></small>
                                </h1>
                            </section>
                            <?php if(!IS_DIALOG && !\think\Config::get('fastadmin.multiplenav') && \think\Config::get('fastadmin.breadcrumb')): ?>
                            <!-- RIBBON -->
                            <div id="ribbon">
                                <ol class="breadcrumb pull-left">
                                    <?php if($auth->check('dashboard')): ?>
                                    <li><a href="dashboard" class="addtabsit"><i class="fa fa-dashboard"></i> <?php echo __('Dashboard'); ?></a></li>
                                    <?php endif; ?>
                                </ol>
                                <ol class="breadcrumb pull-right">
                                    <?php foreach($breadcrumb as $vo): ?>
                                    <li><a href="javascript:;" data-url="<?php echo htmlentities($vo['url'] ?? ''); ?>"><?php echo htmlentities($vo['title'] ?? ''); ?></a></li>
                                    <?php endforeach; ?>
                                </ol>
                            </div>
                            <!-- END RIBBON -->
                            <?php endif; ?>
                            <div class="content">
                                

<style>
.bootstrap-datetimepicker-widget tr:hover {
   background-color: #808080;
}
/* 每列 .schedule-day 最小宽度控制： */
#schedule-columns .schedule-day {
    min-width: 325px;
    flex: 0 0 auto;
    padding-right: 0px;
}

</style>



<div class="panel panel-default panel-intro">
    <?php echo build_heading(); ?>
    <div class="panel-heading" style="padding-bottom: 15px;">
        <form class="form-inline" id="form-search">
            <div class="form-group">
                <label>排片日期：</label>
                <div class="input-group">
                    <input id="c-week_start" type="text" class="form-control datetimepicker" data-start="" data-end=""  placeholder="选择周">
                </div>
          
            </div>
            <div class="form-group" style="margin-left: 20px;">
   
                <button type="button" id="btn-copy-template" class="btn btn-primary" disabled>常用方案拷贝</button>

            </div>
            <div class="form-group">
                <label>目标时间：</label>
                <div class="input-group">
                    <input id="c-week_dend" type="text" class="form-control datetimepicker" data-start="" data-end="" placeholder="选择周">
                </div>
            </div>
        </form>
    </div>
    <div class="panel-body">
        <div id="schedule-columns" class="row" style="display: flex; flex-wrap: nowrap; overflow-x: auto;">
    
            <!-- 7列渲染区域 -->
            <!-- 周一至周日容器 -->
            <div class="col-md-2 schedule-day" data-day="1">
                <div class="panel panel-default">
                    <div class="panel-heading"><span class="day-label">周一</span> <span class="date-label pull-right"></span></div>
                    <div class="panel-body" data-date="">
                        <div class="schedule-cards">
                            <!-- 排期卡片渲染到此处 -->
                        </div>
                        <div class="card-add text-center text-muted" style="cursor:pointer;border:1px dashed #ccc;padding:10px;">
                            + 添加影片排期
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-2 schedule-day" data-day="2">
                <div class="panel panel-default">
                    <div class="panel-heading"><span class="day-label">周二</span> <span class="date-label pull-right"></span></div>
                    <div class="panel-body" data-date="">
                        <div class="schedule-cards">
                            <!-- 排期卡片渲染到此处 -->
                        </div>
                        <div class="card-add text-center text-muted" style="cursor:pointer;border:1px dashed #ccc;padding:10px;">
                            + 添加影片排期
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-2 schedule-day" data-day="3">
                <div class="panel panel-default">
                    <div class="panel-heading"><span class="day-label">周三</span> <span class="date-label pull-right"></span></div>
                    <div class="panel-body" data-date="">
                        <div class="schedule-cards">
                            <!-- 排期卡片渲染到此处 -->
                        </div>
                        <div class="card-add text-center text-muted" style="cursor:pointer;border:1px dashed #ccc;padding:10px;">
                            + 添加影片排期
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-2 schedule-day" data-day="4">
                <div class="panel panel-default">
                    <div class="panel-heading"><span class="day-label">周四</span> <span class="date-label pull-right"></span></div>
                    <div class="panel-body" data-date="">
                        <div class="schedule-cards">
                            <!-- 排期卡片渲染到此处 -->
                        </div>
                        <div class="card-add text-center text-muted" style="cursor:pointer;border:1px dashed #ccc;padding:10px;">
                            + 添加影片排期
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-2 schedule-day" data-day="5">
                <div class="panel panel-default">
                    <div class="panel-heading"><span class="day-label">周五</span> <span class="date-label pull-right"></span></div>
                    <div class="panel-body" data-date="">
                        <div class="schedule-cards">
                            <!-- 排期卡片渲染到此处 -->
                        </div>
                        <div class="card-add text-center text-muted" style="cursor:pointer;border:1px dashed #ccc;padding:10px;">
                            + 添加影片排期
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-2 schedule-day" data-day="6">
                <div class="panel panel-default">
                    <div class="panel-heading"><span class="day-label">周六</span> <span class="date-label pull-right"></span></div>
                    <div class="panel-body" data-date="">
                        <div class="schedule-cards">
                            <!-- 排期卡片渲染到此处 -->
                        </div>
                        <div class="card-add text-center text-muted" style="cursor:pointer;border:1px dashed #ccc;padding:10px;">
                            + 添加影片排期
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-2 schedule-day" data-day="7">
                <div class="panel panel-default">
                    <div class="panel-heading"><span class="day-label">周日</span> <span class="date-label pull-right"></span></div>
                    <div class="panel-body" data-date="">
                        <div class="schedule-cards">
                            <!-- 排期卡片渲染到此处 -->
                        </div>
                        <div class="card-add text-center text-muted" style="cursor:pointer;border:1px dashed #ccc;padding:10px;">
                            + 添加影片排期
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <script src="/assets/js/require.min.js" data-main="/assets/js/require-backend<?php echo \think\Config::get('app_debug')?'':'.min'; ?>.js?v=<?php echo htmlentities($site['version'] ?? ''); ?>"></script>

    </body>
</html>
