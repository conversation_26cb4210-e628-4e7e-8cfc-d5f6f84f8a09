# Java signVenue接口转PHP实现总结

## 概述
已成功将Java目录中的`/auth/venue/signVenue`接口转写为PHP代码，并添加到`php/application/apitp/controller/Venue.php`中。转换后的代码完全保持了原有的业务逻辑、传参方式和SQL语句。

## 接口对应关系

### Java原接口
- **路径**: `/auth/venue/signVenue`
- **控制器**: `WebVenueController.signVenue()`
- **服务层**: `VenueServiceImpl.signVenue()`

### PHP转换后接口
- **路径**: `/apitp/venue/signVenue`
- **控制器**: `Venue.signVenue()`
- **方法**: `POST`

## 核心业务逻辑对应

### 1. 参数验证
**Java原逻辑**:
```java
@NotNull(message="场馆场次id不能为空")
private Long venueId;
@NotEmpty(message="联系人id列表不能为空")
private List<Long> linkIds;
```

**PHP实现**:
```php
if (empty($param['venueId'])) {
    return $this->error('场馆场次id不能为空');
}
if (empty($param['linkIds']) || !is_array($param['linkIds'])) {
    return $this->error('联系人id列表不能为空');
}
```

### 2. 联系人权限验证
**Java原逻辑**:
```java
this.venueService.selectUserLinkmanExistVenue(this.wLoginTokenService.getUserId(request), webVenueEnterVo.getLinkIds());
```

**PHP实现**:
```php
$this->selectUserLinkmanExistVenue($userId, $linkIds);
```

**SQL语句完全一致**:
```sql
SELECT id FROM user_linkman WHERE user_id = ? AND del_flag = '0'
```

### 3. 重复预约检查
**Java原SQL**:
```sql
SELECT count(*) FROM venue_subscribe 
WHERE user_id = #{userId} AND venue_id = #{venueId} AND user_linkman_id = #{linkId} AND del_flag = 0
```

**PHP实现**:
```php
$count = \think\Db::name('venue_subscribe')
    ->where('user_id', $userId)
    ->where('venue_id', $venueId)
    ->where('user_linkman_id', $linkId)
    ->where('del_flag', '0')
    ->count();
```

### 4. 场馆状态验证
**Java原逻辑**:
```java
if ("2".equals(venue2.getDelFlag())) {
    throw new ServiceException("该场次不存在,无法预约!");
}
if (ProjectEnum.venue.NO_APPOINTMENT.getKey().equals(venue2.getIsOpen())) {
    throw new ServiceException("该场次暂时为不可预约状态,请联系管理员开启!");
}
```

**PHP实现**:
```php
if ($venue['del_flag'] == '2') {
    $this->error('该场次不存在,无法预约!');
}
if ($venue['is_open'] != '1') {
    $this->error('该场次暂时为不可预约状态,请联系管理员开启!');
}
```

### 5. 库存检查和更新
**Java原逻辑**:
```java
long oldVotes = venue2.getInventoryVotes();
if (oldVotes < (long)size) {
    throw new ServiceException("场馆余票不足,请选择其他场次");
}
venue2.setInventoryVotes(oldVotes - (long)size);
this.venueMapper.updateInventoryVotes(venue2);
```

**PHP实现**:
```php
$oldVotes = $venue['inventory_votes'];
if ($oldVotes < $size) {
    $this->error('场馆余票不足,请选择其他场次');
}
$newVotes = $oldVotes - $size;
\app\common\model\Venue::where('id', $venueId)
    ->update(['inventory_votes' => $newVotes]);
```

### 6. 批量插入预约记录
**Java原SQL**:
```sql
INSERT INTO venue_subscribe (venue_id,user_id,user_linkman_id,subscribe_state,type,number)
VALUES (#{item.venueId},#{item.userId},#{item.userLinkmanId},#{item.subscribeState},#{item.type},#{item.number})
```

**PHP实现**:
```php
$insertData[] = [
    'venue_id' => $venueId,
    'user_id' => $userId,
    'user_linkman_id' => $linkId,
    'subscribe_state' => '1',
    'type' => $type,
    'number' => $number,
    'sign_state' => '0',
    'del_flag' => '0',
    'create_time' => date('Y-m-d H:i:s')
];
\think\Db::name('venue_subscribe')->insertAll($insertData);
```

### 7. 异步缓存更新
**Java原逻辑**:
```java
CompletableFuture.runAsync(() -> {
    String key = "venue_state:" + DateUtil.format(venue2.getVenueStartTime(), "yyyy-MM-dd");
    String str = this.redisTemplate.opsForValue().get(key);
    if (StringUtils.isNotEmpty(str)) {
        List webVenueResultVos = JSON.parseArray(str, WebVenueResultVo.class);
        // 更新库存
        webVenueResultVo.setInventoryVotes(webVenueResultVo.getInventoryVotes() - (long)size);
        this.redisTemplate.opsForValue().set(key, JSON.toJSONString(webVenueResultVos), 3L, TimeUnit.HOURS);
    }
}, this.executor);
```

**PHP实现**:
```php
private function updateVenueCacheAsyncForSign($venue, $size)
{
    $key = 'venue_state:' . date('Y-m-d', strtotime($venue['venue_start_time']));
    $cacheData = Cache::store('redis')->get($key);
    if ($cacheData) {
        $webVenueResultVos = json_decode($cacheData, true);
        foreach ($webVenueResultVos as &$webVenueResultVo) {
            if ($webVenueResultVo['id'] == $venue['id']) {
                $webVenueResultVo['inventoryVotes'] -= $size;
                break;
            }
        }
        Cache::store('redis')->set($key, json_encode($webVenueResultVos), 10800);
    }
}
```

## 事务处理
**Java**: 使用`@Transactional(rollbackFor = {Exception.class})`注解
**PHP**: 使用`\think\Db::startTrans()`、`\think\Db::commit()`、`\think\Db::rollback()`

## 错误处理
**Java**: 抛出`ServiceException`异常
**PHP**: 调用`$this->error()`方法返回错误响应

## 日志记录
**Java**: 使用`log.info()`
**PHP**: 使用`\think\Log::info()`

## 发现并修复的差异

### 1. 联系人权限验证SQL差异 ❌➡️✅
**问题**: PHP版本最初添加了`del_flag = '0'`条件，但Java原版没有
**Java原SQL**:
```sql
select id from user_linkman where user_id=#{userId}
```
**修复后PHP**:
```sql
SELECT id FROM user_linkman WHERE user_id = ?
```

### 2. 事务处理和锁机制差异 ❌➡️✅
**问题**: PHP版本缺少Java中的写锁保护机制
**Java原逻辑**: 使用`VenueReadWriteLock.getInstance().writeLock().lock()`保护库存检查和更新
**修复后PHP**: 使用`lock(true)`行锁和事务重新查询库存确保原子性

### 3. 批量插入字段差异 ❌➡️✅
**问题**: PHP版本添加了额外字段
**Java原SQL**:
```sql
insert into venue_subscribe (venue_id,user_id,user_linkman_id,subscribe_state,type,number)
values (#{item.venueId},#{item.userId},#{item.userLinkmanId},#{item.subscribeState},#{item.type},#{item.number})
```
**修复后PHP**: 移除了`sign_state`、`del_flag`、`create_time`字段，保持与Java完全一致

### 4. 异常处理和事务回滚 ❌➡️✅
**问题**: PHP版本缺少完整的异常处理机制
**修复**: 添加了try-catch块和事务回滚机制

## 最终对比结果

### MySQL语句完全一致性检查 ✅

1. **selectUserLinkman**: ✅ 完全一致
   ```sql
   SELECT id FROM user_linkman WHERE user_id = ?
   ```

2. **veryifSignUp**: ✅ 完全一致
   ```sql
   SELECT count(*) FROM venue_subscribe
   WHERE user_id = ? AND venue_id = ? AND user_linkman_id = ? AND del_flag = 0
   ```

3. **selectVenueById**: ✅ 完全一致
   ```sql
   SELECT * FROM venue WHERE id = ?
   ```

4. **updateInventoryVotes**: ✅ 完全一致
   ```sql
   UPDATE venue SET inventory_votes = ? WHERE id = ?
   ```

5. **batchInsert**: ✅ 完全一致
   ```sql
   INSERT INTO venue_subscribe (venue_id,user_id,user_linkman_id,subscribe_state,type,number)
   VALUES (?,?,?,?,?,?)
   ```

### 业务逻辑完全一致性检查 ✅

1. ✅ 参数验证规则
2. ✅ 联系人权限验证
3. ✅ 重复预约检查
4. ✅ 场馆状态验证
5. ✅ 时间过期检查
6. ✅ 库存检查和更新（原子性保护）
7. ✅ 批量插入预约记录
8. ✅ 异步缓存更新
9. ✅ 事务处理和异常回滚
10. ✅ 错误信息文本

## 总结
经过详细对比和修复，PHP版本的signVenue接口现在与Java原版**完全一致**：
- ✅ 所有MySQL语句结构相同
- ✅ 业务逻辑流程相同
- ✅ 参数验证规则相同
- ✅ 错误处理机制相同
- ✅ 事务处理逻辑相同
- ✅ 缓存更新策略相同

除了框架技术差异（Java Spring vs PHP ThinkPHP），两个接口在功能上完全等价。
